{"version": 3, "sources": ["../../../src/telemetry/events/version.ts"], "names": ["eventCliSession", "EVENT_VERSION", "hasBabelConfig", "dir", "res", "noopFile", "path", "join", "require", "loadPartialConfig", "cwd", "filename", "sourceFileName", "isForTooling", "options", "presets", "every", "e", "file", "request", "plugins", "length", "hasFilesystemConfig", "nextConfig", "event", "process", "env", "__NEXT_VERSION", "images", "i18n", "payload", "nextVersion", "nodeVersion", "version", "cliCommand", "isSrcDir", "has<PERSON>ow<PERSON><PERSON>", "isCustomServer", "hasNextConfig", "config<PERSON><PERSON><PERSON>", "buildTarget", "hasWebpackConfig", "webpack", "imageEnabled", "imageFutureEnabled", "basePathEnabled", "basePath", "i18nEnabled", "locales", "localeDomainsCount", "domains", "localeDetectionEnabled", "localeDetection", "imageDomainsCount", "imageRemotePatternsCount", "remotePatterns", "imageLocalPatternsCount", "localPatterns", "imageSizes", "imageQualities", "qualities", "imageLoader", "loader", "imageFormats", "formats", "nextConfigOutput", "output", "trailingSlashEnabled", "trailingSlash", "reactStrictMode", "webpackVersion", "turboFlag", "appDir", "pagesDir", "staticStaleTime", "experimental", "staleTimes", "static", "dynamicStaleTime", "dynamic", "eventName"], "mappings": ";;;;+BA2DgBA;;;eAAAA;;;6DA1DC;;;;;;AAEjB,MAAMC,gBAAgB;AAsCtB,SAASC,eAAeC,GAAW;IACjC,IAAI;YAQAC,sBAAAA,cAEKA,sBAAAA;QATP,MAAMC,WAAWC,aAAI,CAACC,IAAI,CAACJ,KAAK;QAChC,MAAMC,MAAMI,QAAQ,iCAAiCC,iBAAiB,CAAC;YACrEC,KAAKP;YACLQ,UAAUN;YACVO,gBAAgBP;QAClB;QACA,MAAMQ,eACJT,EAAAA,eAAAA,IAAIU,OAAO,sBAAXV,uBAAAA,aAAaW,OAAO,qBAApBX,qBAAsBY,KAAK,CACzB,CAACC;gBAAWA;mBAAAA,CAAAA,sBAAAA,UAAAA,EAAGC,IAAI,qBAAPD,QAASE,OAAO,MAAK;eAC9Bf,EAAAA,gBAAAA,IAAIU,OAAO,sBAAXV,uBAAAA,cAAagB,OAAO,qBAApBhB,qBAAsBiB,MAAM,MAAK;QACxC,OAAOjB,IAAIkB,mBAAmB,MAAM,CAACT;IACvC,EAAE,OAAM;QACN,OAAO;IACT;AACF;AAEO,SAASb,gBACdG,GAAW,EACXoB,UAA8B,EAC9BC,KA2BC;QA6CkBD,qCACCA;IA5CpB,wEAAwE;IACxE,IAAI,OAAOE,QAAQC,GAAG,CAACC,cAAc,KAAK,UAAU;QAClD,OAAO,EAAE;IACX;IAEA,MAAM,EAAEC,MAAM,EAAEC,IAAI,EAAE,GAAGN,cAAc,CAAC;IAExC,MAAMO,UAAkC;QACtCC,aAAaN,QAAQC,GAAG,CAACC,cAAc;QACvCK,aAAaP,QAAQQ,OAAO;QAC5BC,YAAYV,MAAMU,UAAU;QAC5BC,UAAUX,MAAMW,QAAQ;QACxBC,YAAYZ,MAAMY,UAAU;QAC5BC,gBAAgBb,MAAMa,cAAc;QACpCC,eAAef,WAAWgB,YAAY,KAAK;QAC3CC,aAAa;QACbC,kBAAkB,QAAOlB,8BAAAA,WAAYmB,OAAO,MAAK;QACjDxC,gBAAgBA,eAAeC;QAC/BwC,cAAc,CAAC,CAACf;QAChBgB,oBAAoB,CAAC,CAAChB;QACtBiB,iBAAiB,CAAC,EAACtB,8BAAAA,WAAYuB,QAAQ;QACvCC,aAAa,CAAC,CAAClB;QACfmB,SAASnB,CAAAA,wBAAAA,KAAMmB,OAAO,IAAGnB,KAAKmB,OAAO,CAACzC,IAAI,CAAC,OAAO;QAClD0C,oBAAoBpB,CAAAA,wBAAAA,KAAMqB,OAAO,IAAGrB,KAAKqB,OAAO,CAAC7B,MAAM,GAAG;QAC1D8B,wBAAwB,CAACtB,OAAO,OAAOA,KAAKuB,eAAe,KAAK;QAChEC,mBAAmBzB,CAAAA,0BAAAA,OAAQsB,OAAO,IAAGtB,OAAOsB,OAAO,CAAC7B,MAAM,GAAG;QAC7DiC,0BAA0B1B,CAAAA,0BAAAA,OAAQ2B,cAAc,IAC5C3B,OAAO2B,cAAc,CAAClC,MAAM,GAC5B;QACJmC,yBAAyB5B,CAAAA,0BAAAA,OAAQ6B,aAAa,IAC1C7B,OAAO6B,aAAa,CAACpC,MAAM,GAC3B;QACJqC,YAAY9B,CAAAA,0BAAAA,OAAQ8B,UAAU,IAAG9B,OAAO8B,UAAU,CAACnD,IAAI,CAAC,OAAO;QAC/DoD,gBAAgB/B,CAAAA,0BAAAA,OAAQgC,SAAS,IAAGhC,OAAOgC,SAAS,CAACrD,IAAI,CAAC,OAAO;QACjEsD,WAAW,EAAEjC,0BAAAA,OAAQkC,MAAM;QAC3BC,cAAcnC,CAAAA,0BAAAA,OAAQoC,OAAO,IAAGpC,OAAOoC,OAAO,CAACzD,IAAI,CAAC,OAAO;QAC3D0D,kBAAkB1C,CAAAA,8BAAAA,WAAY2C,MAAM,KAAI;QACxCC,sBAAsB,CAAC,EAAC5C,8BAAAA,WAAY6C,aAAa;QACjDC,iBAAiB,CAAC,EAAC9C,8BAAAA,WAAY8C,eAAe;QAC9CC,gBAAgB9C,MAAM8C,cAAc,IAAI;QACxCC,WAAW/C,MAAM+C,SAAS,IAAI;QAC9BC,QAAQhD,MAAMgD,MAAM;QACpBC,UAAUjD,MAAMiD,QAAQ;QACxBC,iBAAiBnD,EAAAA,sCAAAA,WAAWoD,YAAY,CAACC,UAAU,qBAAlCrD,oCAAoCsD,MAAM,KAAI;QAC/DC,kBAAkBvD,EAAAA,uCAAAA,WAAWoD,YAAY,CAACC,UAAU,qBAAlCrD,qCAAoCwD,OAAO,KAAI;IACnE;IACA,OAAO;QAAC;YAAEC,WAAW/E;YAAe6B;QAAQ;KAAE;AAChD"}