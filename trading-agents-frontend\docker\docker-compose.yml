version: '3.8'

services:
  # TradingAgents Frontend
  frontend:
    build:
      context: ..
      dockerfile: docker/Dockerfile
    container_name: tradingagents-frontend
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_BASE_URL=${NEXT_PUBLIC_API_BASE_URL:-http://localhost:8000}
      - NEXT_PUBLIC_WS_URL=${NEXT_PUBLIC_WS_URL:-ws://localhost:8000}
      - NEXT_PUBLIC_OPENAI_API_KEY=${NEXT_PUBLIC_OPENAI_API_KEY}
      - NEXT_PUBLIC_FINNHUB_API_KEY=${NEXT_PUBLIC_FINNHUB_API_KEY}
    volumes:
      - ../public:/app/public:ro
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - backend
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # TradingAgents Backend (假设后端也使用 Docker)
  backend:
    image: tradingagents-backend:latest
    container_name: tradingagents-backend
    ports:
      - "8000:8000"
    environment:
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - FINNHUB_API_KEY=${FINNHUB_API_KEY}
    networks:
      - tradingagents-network
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx 反向代理 (可选)
  nginx:
    image: nginx:alpine
    container_name: tradingagents-nginx
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/nginx/ssl:ro
    networks:
      - tradingagents-network
    restart: unless-stopped
    depends_on:
      - frontend
      - backend

networks:
  tradingagents-network:
    driver: bridge
    name: tradingagents-network

volumes:
  nginx-ssl:
    driver: local
