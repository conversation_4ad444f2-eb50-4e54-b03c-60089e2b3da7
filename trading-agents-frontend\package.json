{"name": "trading-agents-frontend", "version": "1.0.0", "description": "TradingAgents 多智能体大语言模型金融交易框架 - 前端界面", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "type-check": "tsc --noEmit"}, "dependencies": {"next": "^14.0.0", "react": "^18.0.0", "react-dom": "^18.0.0", "@types/node": "^20.0.0", "@types/react": "^18.0.0", "@types/react-dom": "^18.0.0", "typescript": "^5.0.0", "tailwindcss": "^3.3.0", "autoprefixer": "^10.4.16", "postcss": "^8.4.31", "@headlessui/react": "^1.7.17", "@heroicons/react": "^2.0.18", "clsx": "^2.0.0", "lucide-react": "^0.292.0", "recharts": "^2.8.0", "axios": "^1.6.0", "socket.io-client": "^4.7.4", "react-hot-toast": "^2.4.1", "framer-motion": "^10.16.4", "date-fns": "^2.30.0", "@tanstack/react-query": "^5.8.4", "zustand": "^4.4.6"}, "devDependencies": {"eslint": "^8.0.0", "eslint-config-next": "^14.0.0", "@tailwindcss/forms": "^0.5.7", "@tailwindcss/typography": "^0.5.10"}, "engines": {"node": ">=18.0.0"}}